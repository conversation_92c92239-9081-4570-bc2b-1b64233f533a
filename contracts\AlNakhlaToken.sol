// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/math/Math.sol";

/**
 * @title AlNakhla Token (NAKH)
 * @dev Advanced ERC20 token with tax mechanisms, anti-whale protection, and security features
 * <AUTHOR> Team
 */
contract AlNakhlaToken is ERC20, ERC20Burnable, ERC20Pausable, Ownable, ReentrancyGuard {
    using Math for uint256;

    // Tax configuration (in basis points, 10000 = 100%)
    uint256 public liquidityTax = 200; // 2%
    uint256 public developmentTax = 100; // 1%
    uint256 public marketingTax = 100; // 1%
    uint256 public constant MAX_TAX = 1000; // 10% maximum total tax

    // Anti-whale configuration
    uint256 public maxTransactionAmount;
    uint256 public maxWalletAmount;
    
    // Tax collection addresses
    address public liquidityWallet;
    address public developmentWallet;
    address public marketingWallet;

    // Exemptions
    mapping(address => bool) public isExemptFromTax;
    mapping(address => bool) public isExemptFromMaxTransaction;
    mapping(address => bool) public isExemptFromMaxWallet;

    // Trading control
    bool public tradingEnabled = false;
    mapping(address => bool) public isAutomatedMarketMakerPair;

    // Events
    event TradingEnabled();
    event TaxUpdated(uint256 liquidityTax, uint256 developmentTax, uint256 marketingTax);
    event WalletUpdated(address indexed wallet, string walletType);
    event ExemptionUpdated(address indexed account, bool exempt, string exemptionType);
    event AutomatedMarketMakerPairUpdated(address indexed pair, bool indexed value);
    event MaxAmountsUpdated(uint256 maxTransaction, uint256 maxWallet);

    constructor(
        string memory name,
        string memory symbol,
        uint256 totalSupply,
        address _liquidityWallet,
        address _developmentWallet,
        address _marketingWallet
    ) ERC20(name, symbol) Ownable(msg.sender) {
        require(_liquidityWallet != address(0), "Liquidity wallet cannot be zero address");
        require(_developmentWallet != address(0), "Development wallet cannot be zero address");
        require(_marketingWallet != address(0), "Marketing wallet cannot be zero address");

        liquidityWallet = _liquidityWallet;
        developmentWallet = _developmentWallet;
        marketingWallet = _marketingWallet;

        // Set initial max amounts (2% of total supply for transactions, 3% for wallet)
        maxTransactionAmount = (totalSupply * 200) / 10000;
        maxWalletAmount = (totalSupply * 300) / 10000;

        // Exempt owner and contract from all restrictions
        _setExemptions(owner(), true);
        _setExemptions(address(this), true);
        _setExemptions(_liquidityWallet, true);
        _setExemptions(_developmentWallet, true);
        _setExemptions(_marketingWallet, true);

        // Mint total supply to owner
        _mint(owner(), totalSupply);
    }

    /**
     * @dev Enable trading (can only be called once)
     */
    function enableTrading() external onlyOwner {
        require(!tradingEnabled, "Trading already enabled");
        tradingEnabled = true;
        emit TradingEnabled();
    }

    /**
     * @dev Update tax rates
     */
    function updateTaxes(
        uint256 _liquidityTax,
        uint256 _developmentTax,
        uint256 _marketingTax
    ) external onlyOwner {
        require(
            _liquidityTax + _developmentTax + _marketingTax <= MAX_TAX,
            "Total tax cannot exceed maximum"
        );
        
        liquidityTax = _liquidityTax;
        developmentTax = _developmentTax;
        marketingTax = _marketingTax;
        
        emit TaxUpdated(_liquidityTax, _developmentTax, _marketingTax);
    }

    /**
     * @dev Update wallet addresses
     */
    function updateWallets(
        address _liquidityWallet,
        address _developmentWallet,
        address _marketingWallet
    ) external onlyOwner {
        require(_liquidityWallet != address(0), "Liquidity wallet cannot be zero address");
        require(_developmentWallet != address(0), "Development wallet cannot be zero address");
        require(_marketingWallet != address(0), "Marketing wallet cannot be zero address");

        liquidityWallet = _liquidityWallet;
        developmentWallet = _developmentWallet;
        marketingWallet = _marketingWallet;

        emit WalletUpdated(_liquidityWallet, "liquidity");
        emit WalletUpdated(_developmentWallet, "development");
        emit WalletUpdated(_marketingWallet, "marketing");
    }

    /**
     * @dev Update max transaction and wallet amounts
     */
    function updateMaxAmounts(uint256 _maxTransactionPercent, uint256 _maxWalletPercent) external onlyOwner {
        require(_maxTransactionPercent >= 50, "Max transaction cannot be less than 0.5%");
        require(_maxWalletPercent >= 100, "Max wallet cannot be less than 1%");
        
        maxTransactionAmount = (totalSupply() * _maxTransactionPercent) / 10000;
        maxWalletAmount = (totalSupply() * _maxWalletPercent) / 10000;
        
        emit MaxAmountsUpdated(maxTransactionAmount, maxWalletAmount);
    }

    /**
     * @dev Set exemptions for an account
     */
    function setExemptions(address account, bool exempt) external onlyOwner {
        _setExemptions(account, exempt);
    }

    function _setExemptions(address account, bool exempt) internal {
        isExemptFromTax[account] = exempt;
        isExemptFromMaxTransaction[account] = exempt;
        isExemptFromMaxWallet[account] = exempt;
        
        emit ExemptionUpdated(account, exempt, "all");
    }

    /**
     * @dev Set automated market maker pair
     */
    function setAutomatedMarketMakerPair(address pair, bool value) external onlyOwner {
        require(pair != address(0), "Pair cannot be zero address");
        isAutomatedMarketMakerPair[pair] = value;
        emit AutomatedMarketMakerPairUpdated(pair, value);
    }

    /**
     * @dev Pause the contract (emergency function)
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause the contract
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev Override transfer function to implement taxes and restrictions
     */
    function _update(address from, address to, uint256 amount) internal override(ERC20, ERC20Pausable) {
        // Allow minting (from zero address) and burning (to zero address)
        if (from == address(0) || to == address(0)) {
            super._update(from, to, amount);
            return;
        }

        // Check if trading is enabled (except for owner operations)
        if (from != owner() && to != owner()) {
            require(tradingEnabled, "Trading not yet enabled");
        }

        // Check max transaction amount
        if (!isExemptFromMaxTransaction[from] && !isExemptFromMaxTransaction[to]) {
            require(amount <= maxTransactionAmount, "Transfer amount exceeds maximum");
        }

        // Check max wallet amount for buys
        if (!isExemptFromMaxWallet[to] && !isAutomatedMarketMakerPair[to]) {
            require(
                balanceOf(to) + amount <= maxWalletAmount,
                "Wallet would exceed maximum balance"
            );
        }

        uint256 transferAmount = amount;

        // Apply taxes if not exempt and not owner operations
        if (
            !isExemptFromTax[from] &&
            !isExemptFromTax[to] &&
            from != owner() &&
            to != owner() &&
            (isAutomatedMarketMakerPair[from] || isAutomatedMarketMakerPair[to])
        ) {
            uint256 totalTax = liquidityTax + developmentTax + marketingTax;
            uint256 taxAmount = (amount * totalTax) / 10000;
            
            if (taxAmount > 0) {
                transferAmount = amount - taxAmount;
                
                // Distribute taxes
                uint256 liquidityAmount = (amount * liquidityTax) / 10000;
                uint256 developmentAmount = (amount * developmentTax) / 10000;
                uint256 marketingAmount = (amount * marketingTax) / 10000;
                
                if (liquidityAmount > 0) super._update(from, liquidityWallet, liquidityAmount);
                if (developmentAmount > 0) super._update(from, developmentWallet, developmentAmount);
                if (marketingAmount > 0) super._update(from, marketingWallet, marketingAmount);
            }
        }

        super._update(from, to, transferAmount);
    }

    /**
     * @dev Get total tax percentage
     */
    function getTotalTax() external view returns (uint256) {
        return liquidityTax + developmentTax + marketingTax;
    }

    /**
     * @dev Emergency withdraw function (only for stuck tokens)
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        require(token != address(this), "Cannot withdraw own tokens");
        IERC20(token).transfer(owner(), amount);
    }

    /**
     * @dev Mint new tokens (only owner)
     */
    function mint(address to, uint256 amount) external onlyOwner {
        _mint(to, amount);
    }
}
