{"_format": "hh-sol-cache-2", "files": {"C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\contracts\\AlNakhlaToken.sol": {"lastModificationDate": 1748790641594, "contentHash": "cf03c03b7a76679819dfe7f5903496f2", "sourceName": "contracts/AlNakhlaToken.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Pausable.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "@openzeppelin/contracts/utils/math/Math.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["AlNakhlaToken"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\ERC20Pausable.sol": {"lastModificationDate": 1748790465095, "contentHash": "a3ce035689a60e32921347c16a3fb786", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Pausable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../utils/Pausable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Pausable"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol": {"lastModificationDate": 1748790465078, "contentHash": "57d79df281f57bbb1b09214c7914f877", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\utils\\ReentrancyGuard.sol": {"lastModificationDate": 1748790465625, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\access\\Ownable.sol": {"lastModificationDate": 1748790465581, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\ERC20Burnable.sol": {"lastModificationDate": 1748790465084, "contentHash": "273d8d24b06f67207dd5f35c3a0c1086", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Burnable"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\utils\\math\\Math.sol": {"lastModificationDate": 1748790465551, "contentHash": "5ec781e33d3a9ac91ffdc83d94420412", "sourceName": "@openzeppelin/contracts/utils/math/Math.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../Panic.sol", "./SafeCast.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Math"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\utils\\Pausable.sol": {"lastModificationDate": 1748790465606, "contentHash": "0d47b53e10b1985efbb396f937626279", "sourceName": "@openzeppelin/contracts/utils/Pausable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Pausable"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\utils\\Context.sol": {"lastModificationDate": 1748790464695, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol": {"lastModificationDate": 1748790464774, "contentHash": "267d92fe4de67b1bdb3302c08f387dbf", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol": {"lastModificationDate": 1748790465355, "contentHash": "8f19f64d2adadf448840908bbaf431c8", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol": {"lastModificationDate": 1748790465361, "contentHash": "794db3115001aa372c79326fcfd44b1f", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20Metadata"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\utils\\Panic.sol": {"lastModificationDate": 1748790465604, "contentHash": "2133dc13536b4a6a98131e431fac59e1", "sourceName": "@openzeppelin/contracts/utils/Panic.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Panic"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\Nakhla\\node_modules\\@openzeppelin\\contracts\\utils\\math\\SafeCast.sol": {"lastModificationDate": 1748790465645, "contentHash": "2adca1150f58fc6f3d1f0a0f22ee7cca", "sourceName": "@openzeppelin/contracts/utils/math/SafeCast.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["SafeCast"]}}}