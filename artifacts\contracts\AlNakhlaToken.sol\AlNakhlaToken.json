{"_format": "hh-sol-artifact-1", "contractName": "AlNakhlaToken", "sourceName": "contracts/AlNakhlaToken.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "address", "name": "_liquidityWallet", "type": "address"}, {"internalType": "address", "name": "_developmentWallet", "type": "address"}, {"internalType": "address", "name": "_marketingWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "pair", "type": "address"}, {"indexed": true, "internalType": "bool", "name": "value", "type": "bool"}], "name": "AutomatedMarketMakerPairUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "exempt", "type": "bool"}, {"indexed": false, "internalType": "string", "name": "exemptionType", "type": "string"}], "name": "ExemptionUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "maxTransaction", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "max<PERSON><PERSON><PERSON>", "type": "uint256"}], "name": "MaxAmountsUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "liquidityTax", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "developmentTax", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "marketingTax", "type": "uint256"}], "name": "TaxUpdated", "type": "event"}, {"anonymous": false, "inputs": [], "name": "TradingEnabled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "wallet", "type": "address"}, {"indexed": false, "internalType": "string", "name": "walletType", "type": "string"}], "name": "WalletUpdated", "type": "event"}, {"inputs": [], "name": "MAX_TAX", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "developmentTax", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "developmentWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "enableTrading", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getTotalTax", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isAutomatedMarketMakerPair", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isExemptFromMaxTransaction", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isExemptFromMaxWallet", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isExemptFromTax", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "liquidityTax", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "liquidityWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "marketingTax", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "marketingWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxTransactionAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxWalletAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "pair", "type": "address"}, {"internalType": "bool", "name": "value", "type": "bool"}], "name": "setAutomatedMarketMakerPair", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bool", "name": "exempt", "type": "bool"}], "name": "setExemptions", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tradingEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_maxTransactionPercent", "type": "uint256"}, {"internalType": "uint256", "name": "_maxWalletPercent", "type": "uint256"}], "name": "updateMaxAmounts", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_liquidityTax", "type": "uint256"}, {"internalType": "uint256", "name": "_developmentTax", "type": "uint256"}, {"internalType": "uint256", "name": "_marketingTax", "type": "uint256"}], "name": "updateTaxes", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_liquidityWallet", "type": "address"}, {"internalType": "address", "name": "_developmentWallet", "type": "address"}, {"internalType": "address", "name": "_marketingWallet", "type": "address"}], "name": "updateWallets", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}