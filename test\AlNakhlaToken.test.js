const { expect } = require("chai");
const { ethers } = require("hardhat");
const { loadFixture } = require("@nomicfoundation/hardhat-toolbox/network-helpers");

describe("AlNakhlaToken", function () {
  async function deployTokenFixture() {
    const [owner, liquidityWallet, developmentWallet, marketingWallet, user1, user2, pair] = await ethers.getSigners();

    const AlNakhlaToken = await ethers.getContractFactory("AlNakhlaToken");
    const totalSupply = ethers.parseEther("1000000000"); // 1 billion tokens
    
    const token = await AlNakhlaToken.deploy(
      "AlNakhla",
      "NAKH",
      totalSupply,
      liquidityWallet.address,
      developmentWallet.address,
      marketingWallet.address
    );

    return {
      token,
      owner,
      liquidityWallet,
      developmentWallet,
      marketingWallet,
      user1,
      user2,
      pair,
      totalSupply
    };
  }

  describe("Deployment", function () {
    it("Should deploy with correct parameters", async function () {
      const { token, owner, liquidityWallet, developmentWallet, marketingWallet, totalSupply } = await loadFixture(deployTokenFixture);

      expect(await token.name()).to.equal("AlNakhla");
      expect(await token.symbol()).to.equal("NAKH");
      expect(await token.decimals()).to.equal(18);
      expect(await token.totalSupply()).to.equal(totalSupply);
      expect(await token.balanceOf(owner.address)).to.equal(totalSupply);
      expect(await token.owner()).to.equal(owner.address);
      expect(await token.liquidityWallet()).to.equal(liquidityWallet.address);
      expect(await token.developmentWallet()).to.equal(developmentWallet.address);
      expect(await token.marketingWallet()).to.equal(marketingWallet.address);
    });

    it("Should set correct initial tax rates", async function () {
      const { token } = await loadFixture(deployTokenFixture);

      expect(await token.liquidityTax()).to.equal(200); // 2%
      expect(await token.developmentTax()).to.equal(100); // 1%
      expect(await token.marketingTax()).to.equal(100); // 1%
      expect(await token.getTotalTax()).to.equal(400); // 4%
    });

    it("Should set correct max amounts", async function () {
      const { token, totalSupply } = await loadFixture(deployTokenFixture);

      const expectedMaxTransaction = (totalSupply * 200n) / 10000n; // 2%
      const expectedMaxWallet = (totalSupply * 300n) / 10000n; // 3%

      expect(await token.maxTransactionAmount()).to.equal(expectedMaxTransaction);
      expect(await token.maxWalletAmount()).to.equal(expectedMaxWallet);
    });

    it("Should exempt owner and wallets from restrictions", async function () {
      const { token, owner, liquidityWallet, developmentWallet, marketingWallet } = await loadFixture(deployTokenFixture);

      expect(await token.isExemptFromTax(owner.address)).to.be.true;
      expect(await token.isExemptFromTax(liquidityWallet.address)).to.be.true;
      expect(await token.isExemptFromTax(developmentWallet.address)).to.be.true;
      expect(await token.isExemptFromTax(marketingWallet.address)).to.be.true;
    });

    it("Should revert with zero address wallets", async function () {
      const [owner] = await ethers.getSigners();
      const AlNakhlaToken = await ethers.getContractFactory("AlNakhlaToken");
      const totalSupply = ethers.parseEther("1000000000");

      await expect(
        AlNakhlaToken.deploy(
          "AlNakhla",
          "NAKH",
          totalSupply,
          ethers.ZeroAddress,
          owner.address,
          owner.address
        )
      ).to.be.revertedWith("Liquidity wallet cannot be zero address");
    });
  });

  describe("Trading Control", function () {
    it("Should start with trading disabled", async function () {
      const { token } = await loadFixture(deployTokenFixture);
      expect(await token.tradingEnabled()).to.be.false;
    });

    it("Should allow owner to enable trading", async function () {
      const { token } = await loadFixture(deployTokenFixture);

      await expect(token.enableTrading())
        .to.emit(token, "TradingEnabled");

      expect(await token.tradingEnabled()).to.be.true;
    });

    it("Should not allow enabling trading twice", async function () {
      const { token } = await loadFixture(deployTokenFixture);

      await token.enableTrading();
      await expect(token.enableTrading())
        .to.be.revertedWith("Trading already enabled");
    });

    it("Should prevent non-owner transfers when trading disabled", async function () {
      const { token, user1, user2 } = await loadFixture(deployTokenFixture);

      // Transfer some tokens to user1 first (owner can transfer)
      await token.transfer(user1.address, ethers.parseEther("1000"));

      // User1 should not be able to transfer when trading is disabled
      await expect(
        token.connect(user1).transfer(user2.address, ethers.parseEther("100"))
      ).to.be.revertedWith("Trading not yet enabled");
    });

    it("Should allow owner transfers when trading disabled", async function () {
      const { token, owner, user1 } = await loadFixture(deployTokenFixture);

      await expect(token.transfer(user1.address, ethers.parseEther("1000")))
        .to.not.be.reverted;

      expect(await token.balanceOf(user1.address)).to.equal(ethers.parseEther("1000"));
    });
  });

  describe("Tax System", function () {
    it("Should update tax rates correctly", async function () {
      const { token } = await loadFixture(deployTokenFixture);

      await expect(token.updateTaxes(300, 200, 100))
        .to.emit(token, "TaxUpdated")
        .withArgs(300, 200, 100);

      expect(await token.liquidityTax()).to.equal(300);
      expect(await token.developmentTax()).to.equal(200);
      expect(await token.marketingTax()).to.equal(100);
      expect(await token.getTotalTax()).to.equal(600);
    });

    it("Should not allow tax rates exceeding maximum", async function () {
      const { token } = await loadFixture(deployTokenFixture);

      await expect(token.updateTaxes(500, 400, 200))
        .to.be.revertedWith("Total tax cannot exceed maximum");
    });

    it("Should apply taxes on AMM pair transactions", async function () {
      const { token, owner, user1, pair, liquidityWallet, developmentWallet, marketingWallet } = await loadFixture(deployTokenFixture);

      // Enable trading and set up AMM pair
      await token.enableTrading();
      await token.setAutomatedMarketMakerPair(pair.address, true);

      // Transfer tokens to user1 and exempt them temporarily to set up
      await token.transfer(user1.address, ethers.parseEther("10000"));
      await token.setExemptions(user1.address, true);
      await token.connect(user1).transfer(pair.address, ethers.parseEther("5000"));
      await token.setExemptions(user1.address, false);

      const transferAmount = ethers.parseEther("1000");
      const expectedTax = (transferAmount * 400n) / 10000n; // 4% total tax
      const expectedLiquidityTax = (transferAmount * 200n) / 10000n; // 2%
      const expectedDevelopmentTax = (transferAmount * 100n) / 10000n; // 1%
      const expectedMarketingTax = (transferAmount * 100n) / 10000n; // 1%
      const expectedTransferAmount = transferAmount - expectedTax;

      const initialBalance = await token.balanceOf(user1.address);
      const initialLiquidityBalance = await token.balanceOf(liquidityWallet.address);
      const initialDevelopmentBalance = await token.balanceOf(developmentWallet.address);
      const initialMarketingBalance = await token.balanceOf(marketingWallet.address);

      // Simulate buy from AMM pair
      await token.connect(pair).transfer(user1.address, transferAmount);

      expect(await token.balanceOf(user1.address)).to.equal(initialBalance + expectedTransferAmount);
      expect(await token.balanceOf(liquidityWallet.address)).to.equal(initialLiquidityBalance + expectedLiquidityTax);
      expect(await token.balanceOf(developmentWallet.address)).to.equal(initialDevelopmentBalance + expectedDevelopmentTax);
      expect(await token.balanceOf(marketingWallet.address)).to.equal(initialMarketingBalance + expectedMarketingTax);
    });

    it("Should not apply taxes to exempt addresses", async function () {
      const { token, owner, user1, pair } = await loadFixture(deployTokenFixture);

      await token.enableTrading();
      await token.setAutomatedMarketMakerPair(pair.address, true);
      await token.setExemptions(user1.address, true);

      const transferAmount = ethers.parseEther("1000");
      await token.transfer(pair.address, transferAmount);

      const initialBalance = await token.balanceOf(user1.address);
      await token.connect(pair).transfer(user1.address, transferAmount);

      expect(await token.balanceOf(user1.address)).to.equal(initialBalance + transferAmount);
    });
  });

  describe("Anti-Whale Protection", function () {
    it("Should enforce max transaction amount", async function () {
      const { token, owner, user1, pair } = await loadFixture(deployTokenFixture);

      await token.enableTrading();
      await token.setAutomatedMarketMakerPair(pair.address, true);

      const maxTransaction = await token.maxTransactionAmount();
      const excessiveAmount = maxTransaction + ethers.parseEther("1");

      await token.transfer(pair.address, excessiveAmount);

      await expect(
        token.connect(pair).transfer(user1.address, excessiveAmount)
      ).to.be.revertedWith("Transfer amount exceeds maximum");
    });

    it("Should enforce max wallet amount", async function () {
      const { token, owner, user1, pair } = await loadFixture(deployTokenFixture);

      await token.enableTrading();
      await token.setAutomatedMarketMakerPair(pair.address, true);

      const maxWallet = await token.maxWalletAmount();
      const maxTransaction = await token.maxTransactionAmount();

      // Transfer enough tokens to the pair for testing
      await token.transfer(pair.address, maxWallet * 2n);

      // Make multiple smaller transfers to fill the wallet to max
      const transferAmount = maxTransaction < maxWallet ? maxTransaction : maxWallet / 2n;

      // First transfer
      await token.connect(pair).transfer(user1.address, transferAmount);

      // Calculate remaining capacity
      const currentBalance = await token.balanceOf(user1.address);
      const remainingCapacity = maxWallet - currentBalance;

      if (remainingCapacity > ethers.parseEther("1")) {
        // Fill up to near max
        await token.connect(pair).transfer(user1.address, remainingCapacity - ethers.parseEther("1"));

        // This should fail as it would exceed max wallet
        await expect(
          token.connect(pair).transfer(user1.address, ethers.parseEther("2"))
        ).to.be.reverted;
      }
    });

    it("Should allow updating max amounts", async function () {
      const { token, totalSupply } = await loadFixture(deployTokenFixture);

      await expect(token.updateMaxAmounts(500, 1000)) // 5% and 10%
        .to.emit(token, "MaxAmountsUpdated");

      const expectedMaxTransaction = (totalSupply * 500n) / 10000n;
      const expectedMaxWallet = (totalSupply * 1000n) / 10000n;

      expect(await token.maxTransactionAmount()).to.equal(expectedMaxTransaction);
      expect(await token.maxWalletAmount()).to.equal(expectedMaxWallet);
    });

    it("Should not allow setting max amounts too low", async function () {
      const { token } = await loadFixture(deployTokenFixture);

      await expect(token.updateMaxAmounts(25, 50)) // 0.25% and 0.5%
        .to.be.revertedWith("Max transaction cannot be less than 0.5%");

      await expect(token.updateMaxAmounts(100, 50)) // 1% and 0.5%
        .to.be.revertedWith("Max wallet cannot be less than 1%");
    });
  });

  describe("Pause Functionality", function () {
    it("Should allow owner to pause and unpause", async function () {
      const { token } = await loadFixture(deployTokenFixture);

      await token.pause();
      expect(await token.paused()).to.be.true;

      await token.unpause();
      expect(await token.paused()).to.be.false;
    });

    it("Should prevent transfers when paused", async function () {
      const { token, user1 } = await loadFixture(deployTokenFixture);

      await token.pause();

      await expect(
        token.transfer(user1.address, ethers.parseEther("100"))
      ).to.be.revertedWithCustomError(token, "EnforcedPause");
    });
  });

  describe("Owner Functions", function () {
    it("Should allow owner to mint tokens", async function () {
      const { token, user1, totalSupply } = await loadFixture(deployTokenFixture);

      const mintAmount = ethers.parseEther("1000");
      await token.mint(user1.address, mintAmount);

      expect(await token.balanceOf(user1.address)).to.equal(mintAmount);
      expect(await token.totalSupply()).to.equal(totalSupply + mintAmount);
    });

    it("Should allow owner to burn tokens", async function () {
      const { token, totalSupply } = await loadFixture(deployTokenFixture);

      const burnAmount = ethers.parseEther("1000");
      await token.burn(burnAmount);

      expect(await token.totalSupply()).to.equal(totalSupply - burnAmount);
    });

    it("Should not allow non-owner to call owner functions", async function () {
      const { token, user1 } = await loadFixture(deployTokenFixture);

      await expect(
        token.connect(user1).enableTrading()
      ).to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount");

      await expect(
        token.connect(user1).updateTaxes(100, 100, 100)
      ).to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount");

      await expect(
        token.connect(user1).pause()
      ).to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount");
    });
  });
});
